const mongoose = require('mongoose');

const eventSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Event title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Event description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  rules: {
    type: String,
    required: [true, 'Event rules are required'],
    maxlength: [2000, 'Rules cannot exceed 2000 characters']
  },
  category: {
    type: String,
    required: [true, 'Event category is required'],
    enum: ['Technical', 'Cultural', 'Sports', 'Literary', 'Gaming', 'Other'],
    default: 'Other'
  },
  posterURL: {
    type: String,
    required: [true, 'Poster URL is required']
  },
  contactInfo: {
    name: {
      type: String,
      required: [true, 'Contact name is required']
    },
    email: {
      type: String,
      required: [true, 'Contact email is required'],
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid contact email'
      ]
    },
    phone: {
      type: String,
      required: [true, 'Contact phone is required'],
      match: [/^\d{10}$/, 'Please enter a valid 10-digit phone number']
    }
  },
  maxParticipants: {
    type: Number,
    default: 100
  },
  registeredParticipants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  eventDate: {
    type: Date,
    required: [true, 'Event date is required']
  },
  venue: {
    type: String,
    required: [true, 'Event venue is required']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  // Winners and Results
  winners: {
    first: {
      participant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      name: String,
      usn: String,
      department: String,
      year: String,
      score: Number,
      remarks: String,
      certificateIssued: {
        type: Boolean,
        default: false
      }
    },
    second: {
      participant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      name: String,
      usn: String,
      department: String,
      year: String,
      score: Number,
      remarks: String,
      certificateIssued: {
        type: Boolean,
        default: false
      }
    },
    third: {
      participant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      name: String,
      usn: String,
      department: String,
      year: String,
      score: Number,
      remarks: String,
      certificateIssued: {
        type: Boolean,
        default: false
      }
    }
  },
  // Event Status
  eventStatus: {
    type: String,
    enum: ['upcoming', 'ongoing', 'completed', 'cancelled'],
    default: 'upcoming'
  },
  // Results metadata
  resultsPublished: {
    type: Boolean,
    default: false
  },
  resultsPublishedAt: {
    type: Date
  },
  resultsPublishedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Virtual for registration count
eventSchema.virtual('registrationCount').get(function() {
  return this.registeredParticipants.length;
});

// Virtual for available spots
eventSchema.virtual('availableSpots').get(function() {
  return this.maxParticipants - this.registeredParticipants.length;
});

module.exports = mongoose.model('Event', eventSchema);
