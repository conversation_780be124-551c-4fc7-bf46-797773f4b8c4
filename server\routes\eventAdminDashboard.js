const express = require('express');
const Event = require('../models/Event');
const User = require('../models/User');
const EventAdmin = require('../models/EventAdmin');
const router = express.Router();

// Event Admin Authentication Middleware (copied from eventAdmin.js)
const eventAdminAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type === 'eventAdmin') {
      const eventAdmin = await EventAdmin.findById(decoded.id)
        .populate('assignedEvent')
        .select('-password');
      
      if (!eventAdmin || !eventAdmin.isActive) {
        return res.status(401).json({ message: 'Event admin not found or inactive' });
      }
      
      req.eventAdmin = eventAdmin;
      req.user = { id: eventAdmin._id, isEventAdmin: true };
    } else {
      const User = require('../models/User');
      const user = await User.findById(decoded.id).select('-password');
      if (!user || !user.isAdmin) {
        return res.status(401).json({ message: 'Not authorized' });
      }
      req.user = user;
    }
    
    next();
  } catch (error) {
    res.status(401).json({ message: 'Token is not valid' });
  }
};

// Permission check middleware
const checkPermission = (permission) => {
  return (req, res, next) => {
    if (!req.eventAdmin) {
      return next(); // Super admin has all permissions
    }
    
    if (!req.eventAdmin.hasPermission(permission)) {
      return res.status(403).json({ 
        message: `Access denied. Required permission: ${permission}` 
      });
    }
    
    next();
  };
};

// @route   GET /api/event-admin-dashboard/event
// @desc    Get assigned event details
// @access  Private (Event Admin)
router.get('/event', eventAdminAuth, checkPermission('canViewEventDetails'), async (req, res) => {
  try {
    const eventId = req.eventAdmin ? req.eventAdmin.assignedEvent._id : req.query.eventId;
    
    const event = await Event.findById(eventId)
      .populate('registeredUsers', 'name email usn department year phone');

    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    res.json({
      success: true,
      event,
      permissions: req.eventAdmin ? req.eventAdmin.permissions : null
    });

  } catch (error) {
    console.error('Get event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/event-admin-dashboard/participants
// @desc    Get event participants
// @access  Private (Event Admin)
router.get('/participants', eventAdminAuth, checkPermission('canViewParticipants'), async (req, res) => {
  try {
    const eventId = req.eventAdmin ? req.eventAdmin.assignedEvent._id : req.query.eventId;
    
    const event = await Event.findById(eventId)
      .populate({
        path: 'registeredUsers',
        select: 'name email usn department year phone createdAt',
        options: { sort: { createdAt: -1 } }
      });

    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // Add registration date to participants
    const participants = event.registeredUsers.map(user => ({
      ...user.toObject(),
      registrationDate: user.createdAt
    }));

    res.json({
      success: true,
      participants,
      totalParticipants: participants.length,
      eventTitle: event.title,
      maxParticipants: event.maxParticipants
    });

  } catch (error) {
    console.error('Get participants error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/event-admin-dashboard/participants/:userId/attendance
// @desc    Mark participant attendance
// @access  Private (Event Admin)
router.post('/participants/:userId/attendance', eventAdminAuth, checkPermission('canMarkAttendance'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { attended, notes } = req.body;
    const eventId = req.eventAdmin.assignedEvent._id;

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is registered for this event
    const event = await Event.findById(eventId);
    if (!event.registeredUsers.includes(userId)) {
      return res.status(400).json({ message: 'User not registered for this event' });
    }

    // Update attendance (you might want to create a separate Attendance model)
    // For now, we'll add it to the user's profile or create a simple tracking system
    
    res.json({
      success: true,
      message: `Attendance ${attended ? 'marked' : 'unmarked'} for ${user.name}`,
      attendance: {
        userId,
        eventId,
        attended,
        notes,
        markedBy: req.eventAdmin._id,
        markedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Mark attendance error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/event-admin-dashboard/participants/:userId
// @desc    Remove participant from event
// @access  Private (Event Admin)
router.delete('/participants/:userId', eventAdminAuth, checkPermission('canRemoveParticipants'), async (req, res) => {
  try {
    const { userId } = req.params;
    const eventId = req.eventAdmin.assignedEvent._id;

    // Remove user from event
    const event = await Event.findByIdAndUpdate(
      eventId,
      { $pull: { registeredUsers: userId } },
      { new: true }
    );

    // Remove event from user's registered events
    await User.findByIdAndUpdate(
      userId,
      { $pull: { registeredEvents: eventId } }
    );

    res.json({
      success: true,
      message: 'Participant removed successfully',
      remainingParticipants: event.registeredUsers.length
    });

  } catch (error) {
    console.error('Remove participant error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/event-admin-dashboard/event
// @desc    Update event details
// @access  Private (Event Admin)
router.put('/event', eventAdminAuth, async (req, res) => {
  try {
    const eventId = req.eventAdmin.assignedEvent._id;
    const updates = {};

    // Check permissions for different fields
    if (req.body.description && req.eventAdmin.hasPermission('canEditEventDescription')) {
      updates.description = req.body.description;
    }

    if (req.body.venue && req.eventAdmin.hasPermission('canEditEventDetails')) {
      updates.venue = req.body.venue;
    }

    if (req.body.eventDate && req.eventAdmin.hasPermission('canEditEventSchedule')) {
      updates.eventDate = req.body.eventDate;
    }

    if (req.body.registrationDeadline && req.eventAdmin.hasPermission('canEditEventDetails')) {
      updates.registrationDeadline = req.body.registrationDeadline;
    }

    if (Object.keys(updates).length === 0) {
      return res.status(403).json({ message: 'No permission to update any fields' });
    }

    const event = await Event.findByIdAndUpdate(
      eventId,
      updates,
      { new: true }
    );

    res.json({
      success: true,
      message: 'Event updated successfully',
      event,
      updatedFields: Object.keys(updates)
    });

  } catch (error) {
    console.error('Update event error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/event-admin-dashboard/close-registration
// @desc    Close event registration
// @access  Private (Event Admin)
router.post('/close-registration', eventAdminAuth, checkPermission('canCloseRegistrations'), async (req, res) => {
  try {
    const eventId = req.eventAdmin.assignedEvent._id;

    const event = await Event.findByIdAndUpdate(
      eventId,
      { 
        registrationDeadline: new Date(), // Set deadline to now
        isRegistrationClosed: true 
      },
      { new: true }
    );

    res.json({
      success: true,
      message: 'Event registration closed successfully',
      event
    });

  } catch (error) {
    console.error('Close registration error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/event-admin-dashboard/analytics
// @desc    Get event analytics
// @access  Private (Event Admin)
router.get('/analytics', eventAdminAuth, checkPermission('canViewAnalytics'), async (req, res) => {
  try {
    const eventId = req.eventAdmin.assignedEvent._id;

    const event = await Event.findById(eventId)
      .populate('registeredUsers', 'department year createdAt');

    // Calculate analytics
    const analytics = {
      totalRegistrations: event.registeredUsers.length,
      maxParticipants: event.maxParticipants,
      registrationRate: ((event.registeredUsers.length / event.maxParticipants) * 100).toFixed(1),
      
      // Department-wise breakdown
      departmentBreakdown: {},
      
      // Year-wise breakdown
      yearBreakdown: {},
      
      // Registration timeline (last 7 days)
      registrationTimeline: []
    };

    // Calculate breakdowns
    event.registeredUsers.forEach(user => {
      // Department breakdown
      const dept = user.department || 'Unknown';
      analytics.departmentBreakdown[dept] = (analytics.departmentBreakdown[dept] || 0) + 1;
      
      // Year breakdown
      const year = user.year || 'Unknown';
      analytics.yearBreakdown[year] = (analytics.yearBreakdown[year] || 0) + 1;
    });

    res.json({
      success: true,
      analytics,
      eventTitle: event.title
    });

  } catch (error) {
    console.error('Get analytics error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/event-admin-dashboard/export-participants
// @desc    Export participants data
// @access  Private (Event Admin)
router.post('/export-participants', eventAdminAuth, checkPermission('canExportData'), async (req, res) => {
  try {
    const eventId = req.eventAdmin.assignedEvent._id;
    const { format = 'csv' } = req.body;

    const event = await Event.findById(eventId)
      .populate('registeredUsers', 'name email usn department year phone createdAt');

    const participants = event.registeredUsers.map((user, index) => ({
      'S.No': index + 1,
      'Name': user.name,
      'Email': user.email,
      'USN': user.usn,
      'Department': user.department,
      'Year': user.year,
      'Phone': user.phone,
      'Registration Date': user.createdAt.toLocaleDateString()
    }));

    res.json({
      success: true,
      data: participants,
      format,
      filename: `${event.title.replace(/\s+/g, '_')}_participants_${new Date().toISOString().split('T')[0]}.${format}`,
      totalRecords: participants.length
    });

  } catch (error) {
    console.error('Export participants error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
