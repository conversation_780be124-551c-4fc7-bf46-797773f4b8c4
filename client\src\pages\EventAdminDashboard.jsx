import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Button, Badge, Tab, Ta<PERSON>, Alert } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import EventAdminParticipants from '../components/EventAdminParticipants';
import EventAdminEventDetails from '../components/EventAdminEventDetails';
import EventAdminAnalytics from '../components/EventAdminAnalytics';

const EventAdminDashboard = () => {
  const [eventAdmin, setEventAdmin] = useState(null);
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('participants');
  
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    const storedEventAdmin = localStorage.getItem('eventAdmin');

    if (!token || !storedEventAdmin) {
      navigate('/event-admin/login');
      return;
    }

    try {
      const adminData = JSON.parse(storedEventAdmin);
      setEventAdmin(adminData);
      fetchEventDetails();
    } catch (error) {
      console.error('Error parsing event admin data:', error);
      navigate('/event-admin/login');
    }
  }, [navigate]);

  const fetchEventDetails = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/event-admin-dashboard/event', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setEvent(data.event);
      } else {
        toast.error(data.message || 'Failed to fetch event details');
      }
    } catch (error) {
      console.error('Error fetching event details:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('eventAdmin');
    toast.success('Logged out successfully');
    navigate('/event-admin/login');
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <div className="spinner-border text-primary mb-3" role="status"></div>
          <p className="text-muted">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!eventAdmin || !event) {
    return (
      <Container className="py-5">
        <Alert variant="danger">
          <Alert.Heading>Access Denied</Alert.Heading>
          <p>Unable to load event admin dashboard. Please try logging in again.</p>
          <Button variant="outline-danger" onClick={() => navigate('/event-admin/login')}>
            Go to Login
          </Button>
        </Alert>
      </Container>
    );
  }

  return (
    <div className="event-admin-dashboard min-vh-100 bg-light">
      {/* Header */}
      <div className="bg-primary text-white py-4">
        <Container>
          <Row className="align-items-center">
            <Col>
              <div className="d-flex align-items-center">
                <div className="me-3">
                  <i className="fas fa-user-shield display-6"></i>
                </div>
                <div>
                  <h2 className="mb-1 fw-bold">Event Admin Dashboard</h2>
                  <p className="mb-0 opacity-75">
                    Welcome back, {eventAdmin.name}
                  </p>
                </div>
              </div>
            </Col>
            <Col xs="auto">
              <Button
                variant="outline-light"
                onClick={handleLogout}
                className="d-flex align-items-center"
              >
                <i className="fas fa-sign-out-alt me-2"></i>
                Logout
              </Button>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Event Info Banner */}
      <div className="bg-white border-bottom py-3">
        <Container>
          <Row className="align-items-center">
            <Col>
              <div className="d-flex align-items-center">
                <Badge bg="primary" className="me-3 px-3 py-2">
                  {event.category}
                </Badge>
                <div>
                  <h4 className="mb-1 fw-semibold">{event.title}</h4>
                  <div className="text-muted small">
                    <i className="fas fa-calendar me-1"></i>
                    {new Date(event.eventDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                    <span className="mx-2">•</span>
                    <i className="fas fa-map-marker-alt me-1"></i>
                    {event.venue}
                  </div>
                </div>
              </div>
            </Col>
            <Col xs="auto">
              <div className="text-end">
                <div className="h5 mb-1 fw-bold text-primary">
                  {event.registeredUsers?.length || 0} / {event.maxParticipants}
                </div>
                <div className="small text-muted">Participants</div>
              </div>
            </Col>
          </Row>
        </Container>
      </div>

      {/* Main Content */}
      <Container className="py-4">
        {/* Instructions */}
        {eventAdmin.instructions && (
          <Alert variant="info" className="mb-4">
            <Alert.Heading className="h6">
              <i className="fas fa-info-circle me-2"></i>
              Instructions
            </Alert.Heading>
            <p className="mb-0">{eventAdmin.instructions}</p>
          </Alert>
        )}

        {/* Dashboard Tabs */}
        <Card className="shadow-sm">
          <Card.Body className="p-0">
            <Tabs
              activeKey={activeTab}
              onSelect={setActiveTab}
              className="nav-fill border-bottom-0"
            >
              {eventAdmin.permissions?.canViewParticipants && (
                <Tab
                  eventKey="participants"
                  title={
                    <span>
                      <i className="fas fa-users me-2"></i>
                      Participants
                      <Badge bg="primary" className="ms-2">
                        {event.registeredUsers?.length || 0}
                      </Badge>
                    </span>
                  }
                >
                  <div className="p-4">
                    <EventAdminParticipants
                      event={event}
                      eventAdmin={eventAdmin}
                      onUpdate={fetchEventDetails}
                    />
                  </div>
                </Tab>
              )}

              {eventAdmin.permissions?.canViewEventDetails && (
                <Tab
                  eventKey="event-details"
                  title={
                    <span>
                      <i className="fas fa-edit me-2"></i>
                      Event Details
                    </span>
                  }
                >
                  <div className="p-4">
                    <EventAdminEventDetails
                      event={event}
                      eventAdmin={eventAdmin}
                      onUpdate={fetchEventDetails}
                    />
                  </div>
                </Tab>
              )}

              {eventAdmin.permissions?.canViewAnalytics && (
                <Tab
                  eventKey="analytics"
                  title={
                    <span>
                      <i className="fas fa-chart-bar me-2"></i>
                      Analytics
                    </span>
                  }
                >
                  <div className="p-4">
                    <EventAdminAnalytics
                      event={event}
                      eventAdmin={eventAdmin}
                    />
                  </div>
                </Tab>
              )}
            </Tabs>
          </Card.Body>
        </Card>

        {/* Permissions Info */}
        <Card className="mt-4 bg-light border-0">
          <Card.Body>
            <h6 className="fw-semibold mb-3">
              <i className="fas fa-key me-2"></i>
              Your Permissions
            </h6>
            <Row className="g-2">
              {Object.entries(eventAdmin.permissions || {}).map(([key, value]) => (
                value && (
                  <Col xs="auto" key={key}>
                    <Badge bg="success" className="text-capitalize">
                      {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                    </Badge>
                  </Col>
                )
              ))}
            </Row>
          </Card.Body>
        </Card>
      </Container>
    </div>
  );
};

export default EventAdminDashboard;
