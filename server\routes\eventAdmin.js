const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const EventAdmin = require('../models/EventAdmin');
const Event = require('../models/Event');
const User = require('../models/User');
const { auth, adminAuth } = require('../middleware/auth');
const router = express.Router();

// Event Admin Authentication Middleware
const eventAdminAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if it's an event admin token
    if (decoded.type === 'eventAdmin') {
      const eventAdmin = await EventAdmin.findById(decoded.id)
        .populate('assignedEvent')
        .select('-password');
      
      if (!eventAdmin || !eventAdmin.isActive) {
        return res.status(401).json({ message: 'Event admin not found or inactive' });
      }
      
      // Check if access period is valid
      const now = new Date();
      if (eventAdmin.accessEndDate && now > eventAdmin.accessEndDate) {
        return res.status(401).json({ message: 'Access period expired' });
      }
      
      if (eventAdmin.accessStartDate && now < eventAdmin.accessStartDate) {
        return res.status(401).json({ message: 'Access not yet active' });
      }
      
      req.eventAdmin = eventAdmin;
      req.user = { id: eventAdmin._id, isEventAdmin: true };
    } else {
      // Regular admin auth
      const user = await User.findById(decoded.id).select('-password');
      if (!user || !user.isAdmin) {
        return res.status(401).json({ message: 'Not authorized' });
      }
      req.user = user;
    }
    
    next();
  } catch (error) {
    res.status(401).json({ message: 'Token is not valid' });
  }
};

// @route   POST /api/event-admin/login
// @desc    Event admin login
// @access  Public
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // Find event admin
    const eventAdmin = await EventAdmin.findByUsername(username)
      .populate('assignedEvent');

    if (!eventAdmin) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Check if account is locked
    if (eventAdmin.isLocked) {
      return res.status(423).json({ 
        message: 'Account temporarily locked due to too many failed login attempts' 
      });
    }

    // Check if account is active
    if (!eventAdmin.isActive) {
      return res.status(403).json({ message: 'Account is deactivated' });
    }

    // Check access period
    const now = new Date();
    if (eventAdmin.accessEndDate && now > eventAdmin.accessEndDate) {
      return res.status(403).json({ message: 'Access period has expired' });
    }

    if (eventAdmin.accessStartDate && now < eventAdmin.accessStartDate) {
      return res.status(403).json({ message: 'Access period has not started yet' });
    }

    // Verify password
    const isMatch = await eventAdmin.comparePassword(password);

    if (!isMatch) {
      await eventAdmin.incLoginAttempts();
      return res.status(400).json({ message: 'Invalid credentials' });
    }

    // Reset login attempts on successful login
    if (eventAdmin.loginAttempts > 0) {
      await eventAdmin.resetLoginAttempts();
    }

    // Update login info
    await EventAdmin.findByIdAndUpdate(eventAdmin._id, {
      lastLogin: new Date(),
      $inc: { loginCount: 1 }
    });

    // Generate JWT token
    const payload = {
      id: eventAdmin._id,
      type: 'eventAdmin',
      eventId: eventAdmin.assignedEvent._id
    };

    const token = jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '8h' });

    res.json({
      success: true,
      token,
      eventAdmin: {
        id: eventAdmin._id,
        username: eventAdmin.username,
        name: eventAdmin.name,
        email: eventAdmin.email,
        assignedEvent: eventAdmin.assignedEvent,
        permissions: eventAdmin.permissions,
        lastLogin: eventAdmin.lastLogin,
        type: 'eventAdmin'
      }
    });

  } catch (error) {
    console.error('Event admin login error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/event-admin/create
// @desc    Create new event admin (Super admin only)
// @access  Private (Super Admin)
router.post('/create', adminAuth, async (req, res) => {
  try {
    const {
      username,
      password,
      name,
      email,
      phone,
      assignedEvent,
      permissions,
      accessEndDate,
      notes,
      instructions
    } = req.body;

    // Check if username already exists
    const existingAdmin = await EventAdmin.findByUsername(username);
    if (existingAdmin) {
      return res.status(400).json({ message: 'Username already exists' });
    }

    // Check if event exists
    const event = await Event.findById(assignedEvent);
    if (!event) {
      return res.status(404).json({ message: 'Event not found' });
    }

    // Create event admin
    const eventAdmin = new EventAdmin({
      username: username.toLowerCase(),
      password,
      name,
      email,
      phone,
      assignedEvent,
      permissions: permissions || {},
      accessEndDate,
      notes,
      instructions,
      createdBy: req.user.id
    });

    await eventAdmin.save();

    // Populate the assigned event
    await eventAdmin.populate('assignedEvent');

    res.status(201).json({
      success: true,
      message: 'Event admin created successfully',
      eventAdmin: {
        id: eventAdmin._id,
        username: eventAdmin.username,
        name: eventAdmin.name,
        email: eventAdmin.email,
        assignedEvent: eventAdmin.assignedEvent,
        permissions: eventAdmin.permissions,
        isActive: eventAdmin.isActive,
        accessEndDate: eventAdmin.accessEndDate,
        createdAt: eventAdmin.createdAt
      },
      credentials: {
        username: eventAdmin.username,
        password: req.body.password, // Return original password for sharing
        loginUrl: `${process.env.CLIENT_URL}/event-admin/login`
      }
    });

  } catch (error) {
    console.error('Create event admin error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/event-admin/list
// @desc    Get all event admins (Super admin only)
// @access  Private (Super Admin)
router.get('/list', adminAuth, async (req, res) => {
  try {
    const eventAdmins = await EventAdmin.find()
      .populate('assignedEvent', 'title category eventDate')
      .populate('createdBy', 'name email')
      .select('-password')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      eventAdmins
    });

  } catch (error) {
    console.error('Get event admins error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/event-admin/profile
// @desc    Get event admin profile
// @access  Private (Event Admin)
router.get('/profile', eventAdminAuth, async (req, res) => {
  try {
    if (!req.eventAdmin) {
      return res.status(403).json({ message: 'Access denied' });
    }

    res.json({
      success: true,
      eventAdmin: {
        id: req.eventAdmin._id,
        username: req.eventAdmin.username,
        name: req.eventAdmin.name,
        email: req.eventAdmin.email,
        phone: req.eventAdmin.phone,
        assignedEvent: req.eventAdmin.assignedEvent,
        permissions: req.eventAdmin.permissions,
        lastLogin: req.eventAdmin.lastLogin,
        loginCount: req.eventAdmin.loginCount,
        instructions: req.eventAdmin.instructions,
        type: 'eventAdmin'
      }
    });

  } catch (error) {
    console.error('Get event admin profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/event-admin/:id/permissions
// @desc    Update event admin permissions (Super admin only)
// @access  Private (Super Admin)
router.put('/:id/permissions', adminAuth, async (req, res) => {
  try {
    const { permissions } = req.body;

    const eventAdmin = await EventAdmin.findByIdAndUpdate(
      req.params.id,
      { permissions },
      { new: true }
    ).populate('assignedEvent');

    if (!eventAdmin) {
      return res.status(404).json({ message: 'Event admin not found' });
    }

    res.json({
      success: true,
      message: 'Permissions updated successfully',
      eventAdmin
    });

  } catch (error) {
    console.error('Update permissions error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/event-admin/:id/status
// @desc    Activate/Deactivate event admin (Super admin only)
// @access  Private (Super Admin)
router.put('/:id/status', adminAuth, async (req, res) => {
  try {
    const { isActive } = req.body;

    const eventAdmin = await EventAdmin.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true }
    ).populate('assignedEvent');

    if (!eventAdmin) {
      return res.status(404).json({ message: 'Event admin not found' });
    }

    res.json({
      success: true,
      message: `Event admin ${isActive ? 'activated' : 'deactivated'} successfully`,
      eventAdmin
    });

  } catch (error) {
    console.error('Update status error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/event-admin/:id
// @desc    Delete event admin (Super admin only)
// @access  Private (Super Admin)
router.delete('/:id', adminAuth, async (req, res) => {
  try {
    const eventAdmin = await EventAdmin.findByIdAndDelete(req.params.id);

    if (!eventAdmin) {
      return res.status(404).json({ message: 'Event admin not found' });
    }

    res.json({
      success: true,
      message: 'Event admin deleted successfully'
    });

  } catch (error) {
    console.error('Delete event admin error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
