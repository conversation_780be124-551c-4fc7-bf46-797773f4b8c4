import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Badge, Modal, Form, Row, Col, Alert, InputGroup } from 'react-bootstrap';
import { toast } from 'react-toastify';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

const EventAdminManagement = () => {
  const [eventAdmins, setEventAdmins] = useState([]);
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showCredentialsModal, setShowCredentialsModal] = useState(false);
  const [newCredentials, setNewCredentials] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    name: '',
    email: '',
    phone: '',
    assignedEvent: '',
    accessEndDate: '',
    notes: '',
    instructions: '',
    permissions: {
      canViewParticipants: true,
      canViewEventDetails: true,
      canViewRegistrations: true,
      canViewAnalytics: false,
      canEditEventDetails: false,
      canEditEventDescription: true,
      canEditEventSchedule: false,
      canUploadPoster: true,
      canAddParticipants: true,
      canRemoveParticipants: true,
      canMarkAttendance: true,
      canSendNotifications: true,
      canAddResults: true,
      canEditResults: false,
      canGenerateCertificates: false,
      canExportData: false,
      canDeleteRegistrations: false,
      canCloseRegistrations: true
    }
  });

  useEffect(() => {
    fetchEventAdmins();
    fetchEvents();
  }, []);

  const fetchEventAdmins = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/event-admin/list`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setEventAdmins(data.eventAdmins);
      }
    } catch (error) {
      console.error('Error fetching event admins:', error);
      toast.error('Failed to fetch event admins');
    } finally {
      setLoading(false);
    }
  };

  const fetchEvents = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/events`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setEvents(data.events);
      }
    } catch (error) {
      console.error('Error fetching events:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.startsWith('permissions.')) {
      const permissionKey = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        permissions: {
          ...prev.permissions,
          [permissionKey]: checked
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const generateUsername = () => {
    const eventName = events.find(e => e._id === formData.assignedEvent)?.title || 'event';
    const cleanName = eventName.toLowerCase().replace(/[^a-z0-9]/g, '');
    const randomNum = Math.floor(Math.random() * 1000);
    return `${cleanName}_admin_${randomNum}`;
  };

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  };

  const handleCreateEventAdmin = async (e) => {
    e.preventDefault();
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/event-admin/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Event admin created successfully!');
        setNewCredentials(data.credentials);
        setShowCreateModal(false);
        setShowCredentialsModal(true);
        fetchEventAdmins();
        
        // Reset form
        setFormData({
          username: '',
          password: '',
          name: '',
          email: '',
          phone: '',
          assignedEvent: '',
          accessEndDate: '',
          notes: '',
          instructions: '',
          permissions: {
            canViewParticipants: true,
            canViewEventDetails: true,
            canViewRegistrations: true,
            canViewAnalytics: false,
            canEditEventDetails: false,
            canEditEventDescription: true,
            canEditEventSchedule: false,
            canUploadPoster: true,
            canAddParticipants: true,
            canRemoveParticipants: true,
            canMarkAttendance: true,
            canSendNotifications: true,
            canAddResults: true,
            canEditResults: false,
            canGenerateCertificates: false,
            canExportData: false,
            canDeleteRegistrations: false,
            canCloseRegistrations: true
          }
        });
      } else {
        toast.error(data.message || 'Failed to create event admin');
      }
    } catch (error) {
      console.error('Error creating event admin:', error);
      toast.error('Network error. Please try again.');
    }
  };

  const toggleEventAdminStatus = async (adminId, currentStatus) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/event-admin/${adminId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ isActive: !currentStatus })
      });

      const data = await response.json();

      if (data.success) {
        toast.success(data.message);
        fetchEventAdmins();
      } else {
        toast.error(data.message || 'Failed to update status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Network error. Please try again.');
    }
  };

  const deleteEventAdmin = async (adminId) => {
    if (!window.confirm('Are you sure you want to delete this event admin?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/event-admin/${adminId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Event admin deleted successfully');
        fetchEventAdmins();
      } else {
        toast.error(data.message || 'Failed to delete event admin');
      }
    } catch (error) {
      console.error('Error deleting event admin:', error);
      toast.error('Network error. Please try again.');
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <div className="spinner-border text-primary" role="status"></div>
        <p className="mt-3 text-muted">Loading event admins...</p>
      </div>
    );
  }

  return (
    <div className="event-admin-management">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h4 className="fw-bold mb-1">Event Admin Management</h4>
          <p className="text-muted mb-0">Create and manage event-specific administrators</p>
        </div>
        <Button
          variant="primary"
          onClick={() => setShowCreateModal(true)}
          className="d-flex align-items-center"
        >
          <i className="fas fa-plus me-2"></i>
          Create Event Admin
        </Button>
      </div>

      {/* Event Admins Table */}
      <Card className="shadow-sm">
        <Card.Body className="p-0">
          {eventAdmins.length === 0 ? (
            <div className="text-center py-5">
              <i className="fas fa-user-shield display-1 text-muted mb-3"></i>
              <h5 className="text-muted">No Event Admins Created</h5>
              <p className="text-muted">Create event-specific administrators to manage individual events.</p>
            </div>
          ) : (
            <div className="table-responsive">
              <Table className="mb-0" hover>
                <thead className="bg-light">
                  <tr>
                    <th className="border-0 py-3">Admin Details</th>
                    <th className="border-0 py-3">Assigned Event</th>
                    <th className="border-0 py-3">Status</th>
                    <th className="border-0 py-3">Last Login</th>
                    <th className="border-0 py-3">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {eventAdmins.map((admin) => (
                    <tr key={admin._id}>
                      <td className="py-3">
                        <div>
                          <div className="fw-semibold">{admin.name}</div>
                          <div className="small text-muted">{admin.email}</div>
                          <div className="small text-muted">@{admin.username}</div>
                        </div>
                      </td>
                      <td className="py-3">
                        <div>
                          <div className="fw-semibold">{admin.assignedEvent?.title}</div>
                          <Badge bg="secondary" className="small">
                            {admin.assignedEvent?.category}
                          </Badge>
                        </div>
                      </td>
                      <td className="py-3">
                        <Badge bg={admin.isActive ? 'success' : 'danger'}>
                          {admin.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td className="py-3">
                        <div className="small">
                          {admin.lastLogin 
                            ? new Date(admin.lastLogin).toLocaleDateString()
                            : 'Never'
                          }
                        </div>
                        <div className="small text-muted">
                          {admin.loginCount} logins
                        </div>
                      </td>
                      <td className="py-3">
                        <div className="d-flex gap-2">
                          <Button
                            variant={admin.isActive ? 'outline-warning' : 'outline-success'}
                            size="sm"
                            onClick={() => toggleEventAdminStatus(admin._id, admin.isActive)}
                          >
                            {admin.isActive ? 'Deactivate' : 'Activate'}
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => deleteEventAdmin(admin._id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Create Event Admin Modal */}
      <Modal show={showCreateModal} onHide={() => setShowCreateModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Create Event Admin</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleCreateEventAdmin}>
          <Modal.Body>
            <Row className="g-3">
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Name *</Form.Label>
                  <Form.Control
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Email *</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Username *</Form.Label>
                  <InputGroup>
                    <Form.Control
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      required
                    />
                    <Button
                      variant="outline-secondary"
                      onClick={() => setFormData(prev => ({ ...prev, username: generateUsername() }))}
                    >
                      Generate
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Password *</Form.Label>
                  <InputGroup>
                    <Form.Control
                      type="text"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                    />
                    <Button
                      variant="outline-secondary"
                      onClick={() => setFormData(prev => ({ ...prev, password: generatePassword() }))}
                    >
                      Generate
                    </Button>
                  </InputGroup>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Assigned Event *</Form.Label>
                  <Form.Select
                    name="assignedEvent"
                    value={formData.assignedEvent}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select an event</option>
                    {events.map(event => (
                      <option key={event._id} value={event._id}>
                        {event.title} ({event.category})
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group>
                  <Form.Label>Access End Date</Form.Label>
                  <Form.Control
                    type="datetime-local"
                    name="accessEndDate"
                    value={formData.accessEndDate}
                    onChange={handleInputChange}
                  />
                  <Form.Text className="text-muted">
                    Leave empty for no expiration
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={12}>
                <Form.Group>
                  <Form.Label>Instructions</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="instructions"
                    value={formData.instructions}
                    onChange={handleInputChange}
                    placeholder="Instructions for the event admin..."
                  />
                </Form.Group>
              </Col>
              <Col md={12}>
                <Form.Group>
                  <Form.Label>Notes</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    placeholder="Internal notes..."
                  />
                </Form.Group>
              </Col>
            </Row>

            {/* Permissions */}
            <hr className="my-4" />
            <h6 className="fw-semibold mb-3">Permissions</h6>
            <Row className="g-2">
              {Object.entries(formData.permissions).map(([key, value]) => (
                <Col md={6} key={key}>
                  <Form.Check
                    type="checkbox"
                    name={`permissions.${key}`}
                    label={key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                    checked={value}
                    onChange={handleInputChange}
                    className="text-capitalize"
                  />
                </Col>
              ))}
            </Row>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button type="submit" variant="primary">
              Create Event Admin
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Credentials Modal */}
      <Modal show={showCredentialsModal} onHide={() => setShowCredentialsModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Event Admin Credentials</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {newCredentials && (
            <Alert variant="success">
              <Alert.Heading className="h6">Event Admin Created Successfully!</Alert.Heading>
              <p className="mb-3">Share these credentials with the event admin:</p>
              
              <div className="bg-light p-3 rounded mb-3">
                <div className="mb-2">
                  <strong>Username:</strong> 
                  <code className="ms-2">{newCredentials.username}</code>
                  <Button
                    variant="link"
                    size="sm"
                    onClick={() => copyToClipboard(newCredentials.username)}
                    className="p-0 ms-2"
                  >
                    <i className="fas fa-copy"></i>
                  </Button>
                </div>
                <div className="mb-2">
                  <strong>Password:</strong> 
                  <code className="ms-2">{newCredentials.password}</code>
                  <Button
                    variant="link"
                    size="sm"
                    onClick={() => copyToClipboard(newCredentials.password)}
                    className="p-0 ms-2"
                  >
                    <i className="fas fa-copy"></i>
                  </Button>
                </div>
                <div>
                  <strong>Login URL:</strong> 
                  <code className="ms-2">{newCredentials.loginUrl}</code>
                  <Button
                    variant="link"
                    size="sm"
                    onClick={() => copyToClipboard(newCredentials.loginUrl)}
                    className="p-0 ms-2"
                  >
                    <i className="fas fa-copy"></i>
                  </Button>
                </div>
              </div>
              
              <p className="small text-muted mb-0">
                <i className="fas fa-exclamation-triangle me-1"></i>
                Make sure to save these credentials securely. The password cannot be retrieved later.
              </p>
            </Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={() => setShowCredentialsModal(false)}>
            Done
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default EventAdminManagement;
