import React, { useState, useEffect } from 'react';
import { Card, Row, Col, ProgressBar, Table } from 'react-bootstrap';
import { toast } from 'react-toastify';

const EventAdminAnalytics = ({ event, eventAdmin }) => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (eventAdmin.permissions?.canViewAnalytics) {
      fetchAnalytics();
    } else {
      setLoading(false);
    }
  }, [eventAdmin.permissions]);

  const fetchAnalytics = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/event-admin-dashboard/analytics', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setAnalytics(data.analytics);
      } else {
        toast.error(data.message || 'Failed to fetch analytics');
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!eventAdmin.permissions?.canViewAnalytics) {
    return (
      <div className="text-center py-5">
        <i className="fas fa-lock display-1 text-muted mb-3"></i>
        <h5 className="text-muted">Access Denied</h5>
        <p className="text-muted">You don't have permission to view analytics.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-5">
        <div className="spinner-border text-primary" role="status"></div>
        <p className="mt-3 text-muted">Loading analytics...</p>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-5">
        <i className="fas fa-chart-bar display-1 text-muted mb-3"></i>
        <h5 className="text-muted">No Analytics Data</h5>
        <p className="text-muted">Analytics data is not available at the moment.</p>
      </div>
    );
  }

  return (
    <div className="event-admin-analytics">
      <div className="mb-4">
        <h5 className="fw-bold mb-1">Event Analytics</h5>
        <p className="text-muted mb-0">Detailed insights about your event registrations</p>
      </div>

      {/* Overview Cards */}
      <Row className="g-4 mb-4">
        <Col md={3}>
          <Card className="text-center border-0 bg-primary bg-opacity-10">
            <Card.Body>
              <i className="fas fa-users display-6 text-primary mb-2"></i>
              <h3 className="fw-bold text-primary mb-1">{analytics.totalRegistrations}</h3>
              <p className="text-muted small mb-0">Total Registrations</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-0 bg-success bg-opacity-10">
            <Card.Body>
              <i className="fas fa-percentage display-6 text-success mb-2"></i>
              <h3 className="fw-bold text-success mb-1">{analytics.registrationRate}%</h3>
              <p className="text-muted small mb-0">Registration Rate</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-0 bg-warning bg-opacity-10">
            <Card.Body>
              <i className="fas fa-user-plus display-6 text-warning mb-2"></i>
              <h3 className="fw-bold text-warning mb-1">{analytics.maxParticipants - analytics.totalRegistrations}</h3>
              <p className="text-muted small mb-0">Spots Available</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-0 bg-info bg-opacity-10">
            <Card.Body>
              <i className="fas fa-chart-line display-6 text-info mb-2"></i>
              <h3 className="fw-bold text-info mb-1">{analytics.maxParticipants}</h3>
              <p className="text-muted small mb-0">Max Capacity</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Registration Progress */}
      <Card className="mb-4">
        <Card.Header>
          <h6 className="mb-0 fw-semibold">
            <i className="fas fa-chart-bar me-2"></i>
            Registration Progress
          </h6>
        </Card.Header>
        <Card.Body>
          <div className="mb-3">
            <div className="d-flex justify-content-between mb-2">
              <span className="fw-semibold">Overall Progress</span>
              <span className="text-muted">{analytics.totalRegistrations} / {analytics.maxParticipants}</span>
            </div>
            <ProgressBar 
              now={parseFloat(analytics.registrationRate)} 
              variant={
                parseFloat(analytics.registrationRate) >= 80 ? 'success' :
                parseFloat(analytics.registrationRate) >= 50 ? 'warning' : 'primary'
              }
              style={{ height: '12px' }}
            />
            <div className="text-center mt-2">
              <small className="text-muted">
                {parseFloat(analytics.registrationRate) >= 90 && 'Almost full! '}
                {parseFloat(analytics.registrationRate) >= 100 ? 'Event is full' : `${analytics.registrationRate}% filled`}
              </small>
            </div>
          </div>
        </Card.Body>
      </Card>

      <Row className="g-4">
        {/* Department Breakdown */}
        <Col md={6}>
          <Card>
            <Card.Header>
              <h6 className="mb-0 fw-semibold">
                <i className="fas fa-building me-2"></i>
                Department Breakdown
              </h6>
            </Card.Header>
            <Card.Body>
              {Object.keys(analytics.departmentBreakdown).length === 0 ? (
                <div className="text-center py-3">
                  <p className="text-muted mb-0">No department data available</p>
                </div>
              ) : (
                <div className="table-responsive">
                  <Table size="sm" className="mb-0">
                    <thead>
                      <tr>
                        <th>Department</th>
                        <th className="text-end">Count</th>
                        <th className="text-end">%</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Object.entries(analytics.departmentBreakdown)
                        .sort(([,a], [,b]) => b - a)
                        .map(([dept, count]) => (
                          <tr key={dept}>
                            <td>{dept}</td>
                            <td className="text-end fw-semibold">{count}</td>
                            <td className="text-end">
                              <small className="text-muted">
                                {((count / analytics.totalRegistrations) * 100).toFixed(1)}%
                              </small>
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </Table>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Year Breakdown */}
        <Col md={6}>
          <Card>
            <Card.Header>
              <h6 className="mb-0 fw-semibold">
                <i className="fas fa-graduation-cap me-2"></i>
                Year Breakdown
              </h6>
            </Card.Header>
            <Card.Body>
              {Object.keys(analytics.yearBreakdown).length === 0 ? (
                <div className="text-center py-3">
                  <p className="text-muted mb-0">No year data available</p>
                </div>
              ) : (
                <div className="table-responsive">
                  <Table size="sm" className="mb-0">
                    <thead>
                      <tr>
                        <th>Year</th>
                        <th className="text-end">Count</th>
                        <th className="text-end">%</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Object.entries(analytics.yearBreakdown)
                        .sort(([a], [b]) => a.localeCompare(b))
                        .map(([year, count]) => (
                          <tr key={year}>
                            <td>Year {year}</td>
                            <td className="text-end fw-semibold">{count}</td>
                            <td className="text-end">
                              <small className="text-muted">
                                {((count / analytics.totalRegistrations) * 100).toFixed(1)}%
                              </small>
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </Table>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Registration Timeline */}
      {analytics.registrationTimeline && analytics.registrationTimeline.length > 0 && (
        <Card className="mt-4">
          <Card.Header>
            <h6 className="mb-0 fw-semibold">
              <i className="fas fa-chart-line me-2"></i>
              Registration Timeline
            </h6>
          </Card.Header>
          <Card.Body>
            <div className="text-center py-3">
              <p className="text-muted mb-0">Registration timeline chart would be displayed here</p>
              <small className="text-muted">Feature coming soon</small>
            </div>
          </Card.Body>
        </Card>
      )}

      {/* Quick Stats */}
      <Card className="mt-4 bg-light border-0">
        <Card.Body>
          <h6 className="fw-semibold mb-3">
            <i className="fas fa-info-circle me-2"></i>
            Quick Stats
          </h6>
          <Row className="g-3">
            <Col md={3}>
              <div className="text-center">
                <div className="h5 mb-1 text-primary">
                  {Object.keys(analytics.departmentBreakdown).length}
                </div>
                <div className="small text-muted">Departments</div>
              </div>
            </Col>
            <Col md={3}>
              <div className="text-center">
                <div className="h5 mb-1 text-success">
                  {Object.keys(analytics.yearBreakdown).length}
                </div>
                <div className="small text-muted">Year Groups</div>
              </div>
            </Col>
            <Col md={3}>
              <div className="text-center">
                <div className="h5 mb-1 text-warning">
                  {analytics.totalRegistrations > 0 ? 
                    Math.max(...Object.values(analytics.departmentBreakdown)) : 0}
                </div>
                <div className="small text-muted">Largest Dept</div>
              </div>
            </Col>
            <Col md={3}>
              <div className="text-center">
                <div className="h5 mb-1 text-info">
                  {analytics.totalRegistrations > 0 ? 
                    Math.max(...Object.values(analytics.yearBreakdown)) : 0}
                </div>
                <div className="small text-muted">Largest Year</div>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>
    </div>
  );
};

export default EventAdminAnalytics;
