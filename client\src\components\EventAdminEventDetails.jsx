import React, { useState } from 'react';
import { Card, Form, Button, Row, Col, Alert } from 'react-bootstrap';
import { toast } from 'react-toastify';

const EventAdminEventDetails = ({ event, eventAdmin, onUpdate }) => {
  const [formData, setFormData] = useState({
    description: event.description || '',
    venue: event.venue || '',
    eventDate: event.eventDate ? new Date(event.eventDate).toISOString().slice(0, 16) : '',
    registrationDeadline: event.registrationDeadline ? new Date(event.registrationDeadline).toISOString().slice(0, 16) : ''
  });
  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setHasChanges(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/event-admin-dashboard/event', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Event details updated successfully');
        setHasChanges(false);
        onUpdate && onUpdate();
      } else {
        toast.error(data.message || 'Failed to update event details');
      }
    } catch (error) {
      console.error('Error updating event:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      description: event.description || '',
      venue: event.venue || '',
      eventDate: event.eventDate ? new Date(event.eventDate).toISOString().slice(0, 16) : '',
      registrationDeadline: event.registrationDeadline ? new Date(event.registrationDeadline).toISOString().slice(0, 16) : ''
    });
    setHasChanges(false);
  };

  const closeRegistration = async () => {
    if (!eventAdmin.permissions?.canCloseRegistrations) {
      toast.error('You do not have permission to close registrations');
      return;
    }

    if (!window.confirm('Are you sure you want to close registration for this event? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/event-admin-dashboard/close-registration', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Event registration closed successfully');
        onUpdate && onUpdate();
      } else {
        toast.error(data.message || 'Failed to close registration');
      }
    } catch (error) {
      console.error('Error closing registration:', error);
      toast.error('Network error. Please try again.');
    }
  };

  const isRegistrationOpen = () => {
    const now = new Date();
    const deadline = new Date(event.registrationDeadline);
    return now < deadline;
  };

  return (
    <div className="event-admin-event-details">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h5 className="fw-bold mb-1">Event Details</h5>
          <p className="text-muted mb-0">Update event information and settings</p>
        </div>
        {eventAdmin.permissions?.canCloseRegistrations && isRegistrationOpen() && (
          <Button
            variant="outline-danger"
            onClick={closeRegistration}
            className="d-flex align-items-center"
          >
            <i className="fas fa-lock me-2"></i>
            Close Registration
          </Button>
        )}
      </div>

      {/* Permissions Alert */}
      <Alert variant="info" className="mb-4">
        <Alert.Heading className="h6">
          <i className="fas fa-info-circle me-2"></i>
          Your Edit Permissions
        </Alert.Heading>
        <div className="small">
          You can edit: {' '}
          {eventAdmin.permissions?.canEditEventDescription && 'Description, '}
          {eventAdmin.permissions?.canEditEventDetails && 'Venue & Registration Deadline, '}
          {eventAdmin.permissions?.canEditEventSchedule && 'Event Date, '}
          {(!eventAdmin.permissions?.canEditEventDescription && 
            !eventAdmin.permissions?.canEditEventDetails && 
            !eventAdmin.permissions?.canEditEventSchedule) && 'No editing permissions granted'}
        </div>
      </Alert>

      <Form onSubmit={handleSubmit}>
        <Row className="g-4">
          {/* Basic Information */}
          <Col md={12}>
            <Card>
              <Card.Header>
                <h6 className="mb-0 fw-semibold">
                  <i className="fas fa-info-circle me-2"></i>
                  Basic Information
                </h6>
              </Card.Header>
              <Card.Body>
                <Row className="g-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label className="fw-semibold">Event Title</Form.Label>
                      <Form.Control
                        type="text"
                        value={event.title}
                        disabled
                        className="bg-light"
                      />
                      <Form.Text className="text-muted">
                        Event title cannot be changed
                      </Form.Text>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-semibold">Category</Form.Label>
                      <Form.Control
                        type="text"
                        value={event.category}
                        disabled
                        className="bg-light"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-semibold">Max Participants</Form.Label>
                      <Form.Control
                        type="number"
                        value={event.maxParticipants}
                        disabled
                        className="bg-light"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label className="fw-semibold">Description</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={4}
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        disabled={!eventAdmin.permissions?.canEditEventDescription}
                        className={!eventAdmin.permissions?.canEditEventDescription ? 'bg-light' : ''}
                      />
                      {!eventAdmin.permissions?.canEditEventDescription && (
                        <Form.Text className="text-muted">
                          You don't have permission to edit the description
                        </Form.Text>
                      )}
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>

          {/* Schedule & Venue */}
          <Col md={12}>
            <Card>
              <Card.Header>
                <h6 className="mb-0 fw-semibold">
                  <i className="fas fa-calendar-alt me-2"></i>
                  Schedule & Venue
                </h6>
              </Card.Header>
              <Card.Body>
                <Row className="g-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-semibold">Event Date & Time</Form.Label>
                      <Form.Control
                        type="datetime-local"
                        name="eventDate"
                        value={formData.eventDate}
                        onChange={handleInputChange}
                        disabled={!eventAdmin.permissions?.canEditEventSchedule}
                        className={!eventAdmin.permissions?.canEditEventSchedule ? 'bg-light' : ''}
                      />
                      {!eventAdmin.permissions?.canEditEventSchedule && (
                        <Form.Text className="text-muted">
                          You don't have permission to edit the event schedule
                        </Form.Text>
                      )}
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-semibold">Registration Deadline</Form.Label>
                      <Form.Control
                        type="datetime-local"
                        name="registrationDeadline"
                        value={formData.registrationDeadline}
                        onChange={handleInputChange}
                        disabled={!eventAdmin.permissions?.canEditEventDetails}
                        className={!eventAdmin.permissions?.canEditEventDetails ? 'bg-light' : ''}
                      />
                      {!eventAdmin.permissions?.canEditEventDetails && (
                        <Form.Text className="text-muted">
                          You don't have permission to edit registration settings
                        </Form.Text>
                      )}
                    </Form.Group>
                  </Col>
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label className="fw-semibold">Venue</Form.Label>
                      <Form.Control
                        type="text"
                        name="venue"
                        value={formData.venue}
                        onChange={handleInputChange}
                        disabled={!eventAdmin.permissions?.canEditEventDetails}
                        className={!eventAdmin.permissions?.canEditEventDetails ? 'bg-light' : ''}
                      />
                      {!eventAdmin.permissions?.canEditEventDetails && (
                        <Form.Text className="text-muted">
                          You don't have permission to edit venue details
                        </Form.Text>
                      )}
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>

          {/* Registration Status */}
          <Col md={12}>
            <Card>
              <Card.Header>
                <h6 className="mb-0 fw-semibold">
                  <i className="fas fa-users me-2"></i>
                  Registration Status
                </h6>
              </Card.Header>
              <Card.Body>
                <Row className="g-3">
                  <Col md={4}>
                    <div className="text-center">
                      <div className="h3 mb-1 text-primary fw-bold">
                        {event.registeredUsers?.length || 0}
                      </div>
                      <div className="small text-muted">Registered</div>
                    </div>
                  </Col>
                  <Col md={4}>
                    <div className="text-center">
                      <div className="h3 mb-1 text-success fw-bold">
                        {event.maxParticipants - (event.registeredUsers?.length || 0)}
                      </div>
                      <div className="small text-muted">Available</div>
                    </div>
                  </Col>
                  <Col md={4}>
                    <div className="text-center">
                      <div className="h3 mb-1 text-warning fw-bold">
                        {Math.round(((event.registeredUsers?.length || 0) / event.maxParticipants) * 100)}%
                      </div>
                      <div className="small text-muted">Filled</div>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Action Buttons */}
        {hasChanges && (
          <div className="d-flex justify-content-end gap-2 mt-4">
            <Button
              variant="outline-secondary"
              onClick={resetForm}
              disabled={loading}
            >
              Reset Changes
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                  Saving...
                </>
              ) : (
                <>
                  <i className="fas fa-save me-2"></i>
                  Save Changes
                </>
              )}
            </Button>
          </div>
        )}
      </Form>
    </div>
  );
};

export default EventAdminEventDetails;
