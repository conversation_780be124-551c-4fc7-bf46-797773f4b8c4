import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Row, Col, Alert, Badge, Card } from 'react-bootstrap';
import { toast } from 'react-toastify';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

const WinnersModal = ({ show, onHide, event, onWinnersUpdated, isEventAdmin = false }) => {
  const [winners, setWinners] = useState({
    first: { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
    second: { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
    third: { name: '', usn: '', department: '', year: '', score: '', remarks: '' }
  });
  const [loading, setLoading] = useState(false);
  const [participants, setParticipants] = useState([]);

  useEffect(() => {
    if (show && event) {
      // Load existing winners if any
      if (event.winners) {
        setWinners({
          first: event.winners.first || { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
          second: event.winners.second || { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
          third: event.winners.third || { name: '', usn: '', department: '', year: '', score: '', remarks: '' }
        });
      }
      
      // Load participants for selection
      fetchParticipants();
    }
  }, [show, event]);

  const fetchParticipants = async () => {
    try {
      const token = localStorage.getItem('token');
      const endpoint = isEventAdmin 
        ? `${API_BASE_URL}/event-admin-dashboard/event`
        : `${API_BASE_URL}/admin/events/${event._id}/participants`;
        
      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (isEventAdmin) {
          setParticipants(data.event?.registeredParticipants || []);
        } else {
          setParticipants(data.participants || []);
        }
      }
    } catch (error) {
      console.error('Error fetching participants:', error);
    }
  };

  const handleInputChange = (position, field, value) => {
    setWinners(prev => ({
      ...prev,
      [position]: {
        ...prev[position],
        [field]: value
      }
    }));
  };

  const handleParticipantSelect = (position, participant) => {
    setWinners(prev => ({
      ...prev,
      [position]: {
        ...prev[position],
        participant: participant._id,
        name: participant.name,
        usn: participant.usn,
        department: participant.department,
        year: participant.year
      }
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      const endpoint = isEventAdmin 
        ? `${API_BASE_URL}/event-admin-dashboard/winners`
        : `${API_BASE_URL}/admin/events/${event._id}/winners`;

      const response = await fetch(endpoint, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          winners,
          ...(isEventAdmin && { eventId: event._id })
        })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Winners updated successfully!');
        onWinnersUpdated && onWinnersUpdated(data.event);
        onHide();
      } else {
        toast.error(data.message || 'Failed to update winners');
      }
    } catch (error) {
      console.error('Error updating winners:', error);
      toast.error('Failed to update winners');
    } finally {
      setLoading(false);
    }
  };

  const renderWinnerForm = (position, title, badgeVariant) => (
    <Card className="mb-3">
      <Card.Header className="d-flex align-items-center">
        <Badge variant={badgeVariant} className="me-2">{title}</Badge>
        <strong>{title} Place</strong>
      </Card.Header>
      <Card.Body>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Select Participant</Form.Label>
              <Form.Select
                value={winners[position].participant || ''}
                onChange={(e) => {
                  const participant = participants.find(p => p._id === e.target.value);
                  if (participant) {
                    handleParticipantSelect(position, participant);
                  }
                }}
              >
                <option value="">Select a participant...</option>
                {participants.map(participant => (
                  <option key={participant._id} value={participant._id}>
                    {participant.name} - {participant.usn} ({participant.department})
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Name</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].name}
                onChange={(e) => handleInputChange(position, 'name', e.target.value)}
                placeholder="Winner's name"
              />
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>USN</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].usn}
                onChange={(e) => handleInputChange(position, 'usn', e.target.value)}
                placeholder="USN"
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Department</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].department}
                onChange={(e) => handleInputChange(position, 'department', e.target.value)}
                placeholder="Department"
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Year</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].year}
                onChange={(e) => handleInputChange(position, 'year', e.target.value)}
                placeholder="Year"
              />
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Score (Optional)</Form.Label>
              <Form.Control
                type="number"
                value={winners[position].score}
                onChange={(e) => handleInputChange(position, 'score', e.target.value)}
                placeholder="Score"
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Remarks (Optional)</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].remarks}
                onChange={(e) => handleInputChange(position, 'remarks', e.target.value)}
                placeholder="Additional remarks"
              />
            </Form.Group>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  );

  return (
    <Modal show={show} onHide={onHide} size="xl" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          🏆 Manage Winners - {event?.title}
        </Modal.Title>
      </Modal.Header>
      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          <Alert variant="info" className="mb-4">
            <strong>Instructions:</strong> Select participants from the dropdown or manually enter winner details. 
            You can add scores and remarks for each position. This will mark the event as completed and publish the results.
          </Alert>

          {renderWinnerForm('first', '1st', 'warning')}
          {renderWinnerForm('second', '2nd', 'secondary')}
          {renderWinnerForm('third', '3rd', 'dark')}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={loading}>
            Cancel
          </Button>
          <Button variant="success" type="submit" disabled={loading}>
            {loading ? 'Updating...' : '🏆 Update Winners'}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default WinnersModal;
