import React, { useState, useEffect } from 'react';
import { Modal, Button, Form, Row, Col, Alert, Badge, Card, InputGroup, ListGroup, Spinner } from 'react-bootstrap';
import { toast } from 'react-toastify';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

const WinnersModal = ({ show, onHide, event, onWinnersUpdated, isEventAdmin = false }) => {
  const [winners, setWinners] = useState({
    first: { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
    second: { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
    third: { name: '', usn: '', department: '', year: '', score: '', remarks: '' }
  });
  const [loading, setLoading] = useState(false);
  const [participants, setParticipants] = useState([]);
  const [searchTerms, setSearchTerms] = useState({
    first: '',
    second: '',
    third: ''
  });
  const [showDropdowns, setShowDropdowns] = useState({
    first: false,
    second: false,
    third: false
  });
  const [loadingParticipants, setLoadingParticipants] = useState(false);

  useEffect(() => {
    if (show && event) {
      // Load existing winners if any
      if (event.winners) {
        const loadedWinners = {
          first: event.winners.first || { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
          second: event.winners.second || { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
          third: event.winners.third || { name: '', usn: '', department: '', year: '', score: '', remarks: '' }
        };
        setWinners(loadedWinners);

        // Initialize search terms with existing winner names
        setSearchTerms({
          first: loadedWinners.first.name || '',
          second: loadedWinners.second.name || '',
          third: loadedWinners.third.name || ''
        });
      } else {
        // Reset everything if no existing winners
        setWinners({
          first: { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
          second: { name: '', usn: '', department: '', year: '', score: '', remarks: '' },
          third: { name: '', usn: '', department: '', year: '', score: '', remarks: '' }
        });
        setSearchTerms({ first: '', second: '', third: '' });
      }

      // Load participants for selection
      fetchParticipants();
    }
  }, [show, event]);

  const fetchParticipants = async () => {
    setLoadingParticipants(true);
    try {
      const token = localStorage.getItem('token');
      const endpoint = isEventAdmin
        ? `${API_BASE_URL}/event-admin-dashboard/event`
        : `${API_BASE_URL}/admin/events/${event._id}/participants`;

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (isEventAdmin) {
          // For event admin, get participants from event details
          setParticipants(data.event?.registeredParticipants || []);
        } else {
          // For main admin, get participants from dedicated endpoint
          setParticipants(data.participants || []);
        }
        console.log('Loaded participants:', data);
      } else {
        toast.error('Failed to load participants');
      }
    } catch (error) {
      console.error('Error fetching participants:', error);
      toast.error('Error loading participants');
    } finally {
      setLoadingParticipants(false);
    }
  };

  const handleInputChange = (position, field, value) => {
    setWinners(prev => ({
      ...prev,
      [position]: {
        ...prev[position],
        [field]: value
      }
    }));
  };

  const handleParticipantSelect = (position, participant) => {
    setWinners(prev => ({
      ...prev,
      [position]: {
        ...prev[position],
        participant: participant._id,
        name: participant.name,
        usn: participant.usn,
        department: participant.department,
        year: participant.year
      }
    }));

    // Clear search and hide dropdown
    setSearchTerms(prev => ({ ...prev, [position]: participant.name }));
    setShowDropdowns(prev => ({ ...prev, [position]: false }));
  };

  // Filter participants based on search term
  const getFilteredParticipants = (position) => {
    const searchTerm = searchTerms[position].toLowerCase();
    if (!searchTerm) return participants;

    return participants.filter(participant =>
      participant.name.toLowerCase().includes(searchTerm) ||
      participant.usn.toLowerCase().includes(searchTerm) ||
      participant.department.toLowerCase().includes(searchTerm) ||
      participant.email.toLowerCase().includes(searchTerm)
    );
  };

  // Handle search input change
  const handleSearchChange = (position, value) => {
    setSearchTerms(prev => ({ ...prev, [position]: value }));
    setShowDropdowns(prev => ({ ...prev, [position]: value.length > 0 }));

    // Clear winner data when search changes
    if (value !== winners[position].name) {
      setWinners(prev => ({
        ...prev,
        [position]: {
          ...prev[position],
          participant: '',
          name: value,
          usn: '',
          department: '',
          year: ''
        }
      }));
    }
  };

  // Handle search focus
  const handleSearchFocus = (position) => {
    if (participants.length > 0) {
      setShowDropdowns(prev => ({ ...prev, [position]: true }));
    }
  };

  // Handle search blur (with delay to allow click on dropdown)
  const handleSearchBlur = (position) => {
    setTimeout(() => {
      setShowDropdowns(prev => ({ ...prev, [position]: false }));
    }, 200);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      const endpoint = isEventAdmin 
        ? `${API_BASE_URL}/event-admin-dashboard/winners`
        : `${API_BASE_URL}/admin/events/${event._id}/winners`;

      const response = await fetch(endpoint, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          winners,
          ...(isEventAdmin && { eventId: event._id })
        })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Winners updated successfully!');
        onWinnersUpdated && onWinnersUpdated(data.event);
        onHide();
      } else {
        toast.error(data.message || 'Failed to update winners');
      }
    } catch (error) {
      console.error('Error updating winners:', error);
      toast.error('Failed to update winners');
    } finally {
      setLoading(false);
    }
  };

  const renderWinnerForm = (position, title, badgeVariant) => (
    <Card className="mb-3">
      <Card.Header className="d-flex align-items-center">
        <Badge variant={badgeVariant} className="me-2">{title}</Badge>
        <strong>{title} Place</strong>
      </Card.Header>
      <Card.Body>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3 position-relative">
              <Form.Label>
                🔍 Search & Select Participant
                {loadingParticipants && <Spinner size="sm" className="ms-2" />}
              </Form.Label>
              <InputGroup>
                <Form.Control
                  type="text"
                  placeholder="Search by name, USN, department, or email..."
                  value={searchTerms[position]}
                  onChange={(e) => handleSearchChange(position, e.target.value)}
                  onFocus={() => handleSearchFocus(position)}
                  onBlur={() => handleSearchBlur(position)}
                />
                <InputGroup.Text>
                  <i className="fas fa-search"></i>
                </InputGroup.Text>
              </InputGroup>

              {/* Search Results Dropdown */}
              {showDropdowns[position] && (
                <div className="position-absolute w-100" style={{ zIndex: 1050, top: '100%' }}>
                  <ListGroup className="shadow-lg border">
                    {getFilteredParticipants(position).length === 0 ? (
                      <ListGroup.Item className="text-muted text-center py-3">
                        <i className="fas fa-search me-2"></i>
                        No participants found matching "{searchTerms[position]}"
                      </ListGroup.Item>
                    ) : (
                      getFilteredParticipants(position).slice(0, 10).map(participant => (
                        <ListGroup.Item
                          key={participant._id}
                          action
                          onClick={() => handleParticipantSelect(position, participant)}
                          className="d-flex justify-content-between align-items-center"
                          style={{ cursor: 'pointer' }}
                        >
                          <div>
                            <div className="fw-bold">{participant.name}</div>
                            <small className="text-muted">
                              {participant.usn} • {participant.department} • {participant.year}
                            </small>
                          </div>
                          <Badge bg="primary">{participant.email}</Badge>
                        </ListGroup.Item>
                      ))
                    )}
                    {getFilteredParticipants(position).length > 10 && (
                      <ListGroup.Item className="text-muted text-center py-2">
                        <small>Showing first 10 results. Refine your search for more specific results.</small>
                      </ListGroup.Item>
                    )}
                  </ListGroup>
                </div>
              )}
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Name</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].name}
                onChange={(e) => handleInputChange(position, 'name', e.target.value)}
                placeholder="Winner's name"
              />
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>USN</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].usn}
                onChange={(e) => handleInputChange(position, 'usn', e.target.value)}
                placeholder="USN"
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Department</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].department}
                onChange={(e) => handleInputChange(position, 'department', e.target.value)}
                placeholder="Department"
              />
            </Form.Group>
          </Col>
          <Col md={4}>
            <Form.Group className="mb-3">
              <Form.Label>Year</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].year}
                onChange={(e) => handleInputChange(position, 'year', e.target.value)}
                placeholder="Year"
              />
            </Form.Group>
          </Col>
        </Row>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Score (Optional)</Form.Label>
              <Form.Control
                type="number"
                value={winners[position].score}
                onChange={(e) => handleInputChange(position, 'score', e.target.value)}
                placeholder="Score"
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Remarks (Optional)</Form.Label>
              <Form.Control
                type="text"
                value={winners[position].remarks}
                onChange={(e) => handleInputChange(position, 'remarks', e.target.value)}
                placeholder="Additional remarks"
              />
            </Form.Group>
          </Col>
        </Row>
      </Card.Body>
    </Card>
  );

  return (
    <Modal show={show} onHide={onHide} size="xl" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          🏆 Manage Winners - {event?.title}
        </Modal.Title>
      </Modal.Header>
      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          <Alert variant="info" className="mb-4">
            <div className="d-flex align-items-start">
              <i className="fas fa-info-circle me-2 mt-1"></i>
              <div>
                <strong>How to Add Winners:</strong>
                <ul className="mb-2 mt-2">
                  <li><strong>🔍 Search Participants:</strong> Type name, USN, department, or email to find registered participants</li>
                  <li><strong>📝 Manual Entry:</strong> You can also manually enter winner details if they're not in the participant list</li>
                  <li><strong>🏆 Complete Details:</strong> Add scores and remarks for each position (optional)</li>
                  <li><strong>✅ Auto-Complete:</strong> When you select a participant, their details will be auto-filled</li>
                </ul>
                <Badge bg="warning" className="me-2">
                  <i className="fas fa-exclamation-triangle me-1"></i>
                  This will mark the event as completed and publish results
                </Badge>
                <Badge bg="info">
                  <i className="fas fa-users me-1"></i>
                  {participants.length} registered participants
                </Badge>
              </div>
            </div>
          </Alert>

          {renderWinnerForm('first', '1st', 'warning')}
          {renderWinnerForm('second', '2nd', 'secondary')}
          {renderWinnerForm('third', '3rd', 'dark')}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={loading}>
            Cancel
          </Button>
          <Button variant="success" type="submit" disabled={loading}>
            {loading ? 'Updating...' : '🏆 Update Winners'}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default WinnersModal;
