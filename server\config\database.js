const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    // Use MongoDB Atlas free tier or local MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/altius2k25';

    const conn = await mongoose.connect(mongoURI);
    console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
    console.log(`📊 Database: ${conn.connection.name}`);
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    console.log('⚠️  MongoDB not available. Some features may not work.');
    console.log('💡 To fix this:');
    console.log('   1. Install MongoDB locally, or');
    console.log('   2. Use MongoDB Atlas (cloud) and update MONGODB_URI in .env');
    console.log('   3. For development, fallback data will be used');
    // Don't exit the process, let the server run without <PERSON> for demo
  }
};

module.exports = connectDB;
