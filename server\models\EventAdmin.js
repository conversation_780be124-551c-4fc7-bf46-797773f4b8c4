const mongoose = require('mongoose');

const eventAdminSchema = new mongoose.Schema({
  // Basic Information
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  phone: {
    type: String,
    trim: true
  },
  
  // Event Assignment
  assignedEvent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Event',
    required: true
  },
  
  // Permissions
  permissions: {
    // View permissions
    canViewParticipants: {
      type: Boolean,
      default: true
    },
    canViewEventDetails: {
      type: Boolean,
      default: true
    },
    canViewRegistrations: {
      type: Boolean,
      default: true
    },
    canViewAnalytics: {
      type: Boolean,
      default: false
    },
    
    // Edit permissions
    canEditEventDetails: {
      type: <PERSON>olean,
      default: false
    },
    canEditEventDescription: {
      type: Boolean,
      default: true
    },
    canEditEventSchedule: {
      type: Boolean,
      default: false
    },
    canUploadPoster: {
      type: Boolean,
      default: true
    },
    
    // Participant management
    canAddParticipants: {
      type: Boolean,
      default: true
    },
    canRemoveParticipants: {
      type: Boolean,
      default: true
    },
    canMarkAttendance: {
      type: Boolean,
      default: true
    },
    canSendNotifications: {
      type: Boolean,
      default: true
    },
    
    // Results and scoring
    canAddResults: {
      type: Boolean,
      default: true
    },
    canEditResults: {
      type: Boolean,
      default: false
    },
    canGenerateCertificates: {
      type: Boolean,
      default: false
    },
    
    // Advanced permissions
    canExportData: {
      type: Boolean,
      default: false
    },
    canDeleteRegistrations: {
      type: Boolean,
      default: false
    },
    canCloseRegistrations: {
      type: Boolean,
      default: true
    }
  },
  
  // Status and metadata
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastLogin: {
    type: Date
  },
  loginCount: {
    type: Number,
    default: 0
  },
  
  // Access restrictions
  accessStartDate: {
    type: Date,
    default: Date.now
  },
  accessEndDate: {
    type: Date
  },
  maxLoginAttempts: {
    type: Number,
    default: 5
  },
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockedUntil: {
    type: Date
  },
  
  // Notes and instructions
  notes: {
    type: String,
    maxlength: 500
  },
  instructions: {
    type: String,
    maxlength: 1000
  }
}, {
  timestamps: true
});

// Indexes (username already has unique index from schema definition)
eventAdminSchema.index({ assignedEvent: 1 });
eventAdminSchema.index({ createdBy: 1 });
eventAdminSchema.index({ isActive: 1 });

// Virtual for checking if account is locked
eventAdminSchema.virtual('isLocked').get(function() {
  return !!(this.lockedUntil && this.lockedUntil > Date.now());
});

// Methods
eventAdminSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockedUntil && this.lockedUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockedUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // Lock account after max attempts
  if (this.loginAttempts + 1 >= this.maxLoginAttempts && !this.isLocked) {
    updates.$set = { lockedUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

eventAdminSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockedUntil: 1 }
  });
};

// Check if admin has specific permission
eventAdminSchema.methods.hasPermission = function(permission) {
  return this.permissions[permission] === true;
};

// Get all granted permissions
eventAdminSchema.methods.getGrantedPermissions = function() {
  const granted = [];
  for (const [key, value] of Object.entries(this.permissions.toObject())) {
    if (value === true) {
      granted.push(key);
    }
  }
  return granted;
};

// Pre-save middleware to hash password
eventAdminSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const bcrypt = require('bcryptjs');
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
eventAdminSchema.methods.comparePassword = async function(candidatePassword) {
  const bcrypt = require('bcryptjs');
  return bcrypt.compare(candidatePassword, this.password);
};

// Static method to find by username
eventAdminSchema.statics.findByUsername = function(username) {
  return this.findOne({ username: username.toLowerCase() });
};

module.exports = mongoose.model('EventAdmin', eventAdminSchema);
