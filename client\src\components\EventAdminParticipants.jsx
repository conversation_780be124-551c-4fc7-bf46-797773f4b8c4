import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Badge, Form, InputGroup, Modal, Alert, Card, Row, Col } from 'react-bootstrap';
import { toast } from 'react-toastify';

const EventAdminParticipants = ({ event, eventAdmin, onUpdate }) => {
  const [participants, setParticipants] = useState([]);
  const [filteredParticipants, setFilteredParticipants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('');
  const [filterYear, setFilterYear] = useState('');
  const [showExportModal, setShowExportModal] = useState(false);
  const [selectedParticipants, setSelectedParticipants] = useState([]);

  useEffect(() => {
    fetchParticipants();
  }, []);

  useEffect(() => {
    filterParticipants();
  }, [participants, searchTerm, filterDepartment, filterYear]);

  const fetchParticipants = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/event-admin-dashboard/participants', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setParticipants(data.participants);
      } else {
        toast.error(data.message || 'Failed to fetch participants');
      }
    } catch (error) {
      console.error('Error fetching participants:', error);
      toast.error('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filterParticipants = () => {
    let filtered = participants;

    if (searchTerm) {
      filtered = filtered.filter(p =>
        p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.usn.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (filterDepartment) {
      filtered = filtered.filter(p => p.department === filterDepartment);
    }

    if (filterYear) {
      filtered = filtered.filter(p => p.year === filterYear);
    }

    setFilteredParticipants(filtered);
  };

  const removeParticipant = async (participantId, participantName) => {
    if (!eventAdmin.permissions?.canRemoveParticipants) {
      toast.error('You do not have permission to remove participants');
      return;
    }

    if (!window.confirm(`Are you sure you want to remove ${participantName} from this event?`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/event-admin-dashboard/participants/${participantId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Participant removed successfully');
        fetchParticipants();
        onUpdate && onUpdate();
      } else {
        toast.error(data.message || 'Failed to remove participant');
      }
    } catch (error) {
      console.error('Error removing participant:', error);
      toast.error('Network error. Please try again.');
    }
  };

  const markAttendance = async (participantId, attended) => {
    if (!eventAdmin.permissions?.canMarkAttendance) {
      toast.error('You do not have permission to mark attendance');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/event-admin-dashboard/participants/${participantId}/attendance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ attended })
      });

      const data = await response.json();
      if (data.success) {
        toast.success(data.message);
        // Update local state
        setParticipants(prev => prev.map(p => 
          p._id === participantId ? { ...p, attended } : p
        ));
      } else {
        toast.error(data.message || 'Failed to mark attendance');
      }
    } catch (error) {
      console.error('Error marking attendance:', error);
      toast.error('Network error. Please try again.');
    }
  };

  const exportParticipants = async () => {
    if (!eventAdmin.permissions?.canExportData) {
      toast.error('You do not have permission to export data');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/event-admin-dashboard/export-participants', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ format: 'csv' })
      });

      const data = await response.json();
      if (data.success) {
        // Convert to CSV and download
        const csvContent = convertToCSV(data.data);
        downloadCSV(csvContent, data.filename);
        toast.success('Participants data exported successfully');
        setShowExportModal(false);
      } else {
        toast.error(data.message || 'Failed to export data');
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Network error. Please try again.');
    }
  };

  const convertToCSV = (data) => {
    if (!data.length) return '';
    
    const headers = Object.keys(data[0]);
    const csvRows = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ];
    
    return csvRows.join('\n');
  };

  const downloadCSV = (csvContent, filename) => {
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getDepartments = () => {
    return [...new Set(participants.map(p => p.department).filter(Boolean))];
  };

  const getYears = () => {
    return [...new Set(participants.map(p => p.year).filter(Boolean))];
  };

  if (loading) {
    return (
      <div className="text-center py-5">
        <div className="spinner-border text-primary" role="status"></div>
        <p className="mt-3 text-muted">Loading participants...</p>
      </div>
    );
  }

  return (
    <div className="event-admin-participants">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h5 className="fw-bold mb-1">Event Participants</h5>
          <p className="text-muted mb-0">
            {filteredParticipants.length} of {participants.length} participants
          </p>
        </div>
        <div className="d-flex gap-2">
          {eventAdmin.permissions?.canExportData && (
            <Button
              variant="outline-success"
              onClick={() => setShowExportModal(true)}
              className="d-flex align-items-center"
            >
              <i className="fas fa-download me-2"></i>
              Export
            </Button>
          )}
          {eventAdmin.permissions?.canCloseRegistrations && (
            <Button
              variant="outline-warning"
              onClick={() => {/* Implement close registration */}}
              className="d-flex align-items-center"
            >
              <i className="fas fa-lock me-2"></i>
              Close Registration
            </Button>
          )}
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-4">
        <Card.Body>
          <Row className="g-3">
            <Col md={4}>
              <Form.Group>
                <Form.Label className="small fw-semibold">Search</Form.Label>
                <InputGroup>
                  <InputGroup.Text>
                    <i className="fas fa-search"></i>
                  </InputGroup.Text>
                  <Form.Control
                    type="text"
                    placeholder="Search by name, email, or USN..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </InputGroup>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label className="small fw-semibold">Department</Form.Label>
                <Form.Select
                  value={filterDepartment}
                  onChange={(e) => setFilterDepartment(e.target.value)}
                >
                  <option value="">All Departments</option>
                  {getDepartments().map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label className="small fw-semibold">Year</Form.Label>
                <Form.Select
                  value={filterYear}
                  onChange={(e) => setFilterYear(e.target.value)}
                >
                  <option value="">All Years</option>
                  {getYears().map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Participants Table */}
      {filteredParticipants.length === 0 ? (
        <Card>
          <Card.Body className="text-center py-5">
            <i className="fas fa-users display-1 text-muted mb-3"></i>
            <h5 className="text-muted">No Participants Found</h5>
            <p className="text-muted">
              {participants.length === 0 
                ? 'No one has registered for this event yet.'
                : 'No participants match your current filters.'
              }
            </p>
          </Card.Body>
        </Card>
      ) : (
        <Card>
          <div className="table-responsive">
            <Table className="mb-0" hover>
              <thead className="bg-light">
                <tr>
                  <th className="border-0 py-3">Participant</th>
                  <th className="border-0 py-3">Contact</th>
                  <th className="border-0 py-3">Academic Info</th>
                  <th className="border-0 py-3">Registration</th>
                  {eventAdmin.permissions?.canMarkAttendance && (
                    <th className="border-0 py-3">Attendance</th>
                  )}
                  <th className="border-0 py-3">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredParticipants.map((participant) => (
                  <tr key={participant._id}>
                    <td className="py-3">
                      <div>
                        <div className="fw-semibold">{participant.name}</div>
                        <div className="small text-muted">{participant.usn}</div>
                      </div>
                    </td>
                    <td className="py-3">
                      <div>
                        <div className="small">{participant.email}</div>
                        <div className="small text-muted">{participant.phone}</div>
                      </div>
                    </td>
                    <td className="py-3">
                      <div>
                        <div className="small">{participant.department}</div>
                        <div className="small text-muted">Year {participant.year}</div>
                      </div>
                    </td>
                    <td className="py-3">
                      <div className="small">
                        {new Date(participant.registrationDate || participant.createdAt).toLocaleDateString()}
                      </div>
                      <Badge bg="success" className="small">Registered</Badge>
                    </td>
                    {eventAdmin.permissions?.canMarkAttendance && (
                      <td className="py-3">
                        <div className="d-flex gap-1">
                          <Button
                            variant={participant.attended ? 'success' : 'outline-success'}
                            size="sm"
                            onClick={() => markAttendance(participant._id, true)}
                          >
                            Present
                          </Button>
                          <Button
                            variant={participant.attended === false ? 'danger' : 'outline-danger'}
                            size="sm"
                            onClick={() => markAttendance(participant._id, false)}
                          >
                            Absent
                          </Button>
                        </div>
                      </td>
                    )}
                    <td className="py-3">
                      {eventAdmin.permissions?.canRemoveParticipants && (
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => removeParticipant(participant._id, participant.name)}
                        >
                          <i className="fas fa-trash"></i>
                        </Button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
        </Card>
      )}

      {/* Export Modal */}
      <Modal show={showExportModal} onHide={() => setShowExportModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Export Participants</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Alert variant="info">
            <Alert.Heading className="h6">Export Details</Alert.Heading>
            <ul className="mb-0">
              <li>Total participants: {participants.length}</li>
              <li>Format: CSV (Excel compatible)</li>
              <li>Includes: Name, Email, USN, Department, Year, Phone, Registration Date</li>
            </ul>
          </Alert>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowExportModal(false)}>
            Cancel
          </Button>
          <Button variant="success" onClick={exportParticipants}>
            <i className="fas fa-download me-2"></i>
            Export CSV
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default EventAdminParticipants;
