import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Badge, Alert, Modal } from 'react-bootstrap';
import { useAuth } from '../context/AuthContext';
import { adminAPI } from '../services/api';
import { formatDate, getCategoryColor } from '../utils/helpers';
import Loading from '../components/Loading';
import EventFormModal from '../components/EventFormModal';
import EventViewModal from '../components/EventViewModal';
import ParticipantsModal from '../components/ParticipantsModal';
import UserManagementModal from '../components/UserManagementModal';
import EventAdminManagement from '../components/EventAdminManagement';
import { toast } from 'react-toastify';

const Admin = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showEventModal, setShowEventModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showParticipantsModal, setShowParticipantsModal] = useState(false);
  const [showUserManagementModal, setShowUserManagementModal] = useState(false);
  const [showEventAdminModal, setShowEventAdminModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (user?.isAdmin) {
      fetchDashboardData();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      const response = await adminAPI.getDashboard();
      setDashboardData(response.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load admin dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = async (eventId) => {
    if (!window.confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
      return;
    }

    try {
      await adminAPI.deleteEvent(eventId);
      toast.success('Event deleted successfully');
      fetchDashboardData(); // Refresh data
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to delete event';
      toast.error(message);
    }
  };

  // Handle creating new event
  const handleCreateEvent = () => {
    setSelectedEvent(null);
    setIsEditing(false);
    setShowEventModal(true);
  };

  // Handle editing event
  const handleEditEvent = (event) => {
    setSelectedEvent(event);
    setIsEditing(true);
    setShowViewModal(false);
    setShowEventModal(true);
  };

  // Handle viewing event
  const handleViewEvent = (event) => {
    setSelectedEvent(event);
    setShowViewModal(true);
  };

  // Handle export data
  const handleExportData = () => {
    try {
      const dataStr = JSON.stringify(dashboardData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `altius-admin-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success('Data exported successfully!');
    } catch (error) {
      toast.error('Failed to export data');
    }
  };

  // Handle bulk upload (placeholder)
  const handleBulkUpload = () => {
    toast.info('Bulk upload feature coming soon!');
  };

  // Handle settings - now opens user management
  const handleSettings = () => {
    setShowUserManagementModal(true);
  };

  // Handle viewing participants
  const handleViewParticipants = (event) => {
    setSelectedEvent(event);
    setShowParticipantsModal(true);
  };

  // Handle modal success
  const handleModalSuccess = () => {
    fetchDashboardData();
    setShowEventModal(false);
    setSelectedEvent(null);
  };

  if (!user?.isAdmin) {
    return (
      <Container className="py-5">
        <Row className="justify-content-center">
          <Col md={6} className="text-center">
            <Alert variant="danger">
              <i className="fas fa-exclamation-triangle display-1 mb-3"></i>
              <h4>Access Denied</h4>
              <p>You don't have permission to access the admin panel.</p>
            </Alert>
          </Col>
        </Row>
      </Container>
    );
  }

  if (loading) {
    return <Loading fullScreen text="Loading admin dashboard..." />;
  }

  return (
    <div className="admin-page py-3 py-md-5">
      <Container fluid className="px-3 px-md-4">
        {/* Header */}
        <Row className="mb-4">
          <Col>
            <h1 className="display-5 fw-bold text-dark mb-2">Admin Dashboard</h1>
            <p className="text-muted">Manage events and monitor ALTIUS 2K25 activities</p>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-5">
          <Col xl={3} lg={6} md={6} sm={6} xs={12} className="mb-3">
            <Card className="bg-primary text-white h-100">
              <Card.Body className="text-center">
                <i className="fas fa-calendar display-4 mb-3"></i>
                <h3 className="fw-bold">{dashboardData?.stats?.totalEvents || 0}</h3>
                <p className="mb-0">Total Events</p>
              </Card.Body>
            </Card>
          </Col>
          <Col xl={3} lg={6} md={6} sm={6} xs={12} className="mb-3">
            <Card className="bg-success text-white h-100">
              <Card.Body className="text-center">
                <i className="fas fa-users display-4 mb-3"></i>
                <h3 className="fw-bold">{dashboardData?.stats?.totalUsers || 0}</h3>
                <p className="mb-0">Total Users</p>
              </Card.Body>
            </Card>
          </Col>
          <Col xl={3} lg={6} md={6} sm={6} xs={12} className="mb-3">
            <Card className="bg-warning text-dark h-100">
              <Card.Body className="text-center">
                <i className="fas fa-check-circle display-4 mb-3"></i>
                <h3 className="fw-bold">{dashboardData?.stats?.activeEvents || 0}</h3>
                <p className="mb-0">Active Events</p>
              </Card.Body>
            </Card>
          </Col>
          <Col xl={3} lg={6} md={6} sm={6} xs={12} className="mb-3">
            <Card className="bg-info text-white h-100">
              <Card.Body className="text-center">
                <i className="fas fa-user-check display-4 mb-3"></i>
                <h3 className="fw-bold">{dashboardData?.stats?.totalRegistrations || 0}</h3>
                <p className="mb-0">Total Registrations</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Quick Actions */}
        <Row className="mb-4">
          <Col>
            <Card>
              <Card.Header>
                <h5 className="mb-0 fw-semibold">Quick Actions</h5>
              </Card.Header>
              <Card.Body>
                <div className="d-flex gap-2 gap-md-3 flex-wrap">
                  <Button variant="primary" onClick={handleCreateEvent}>
                    <i className="fas fa-plus me-2"></i>
                    Add New Event
                  </Button>
                  <Button variant="success" onClick={handleBulkUpload}>
                    <i className="fas fa-upload me-2"></i>
                    Bulk Upload
                  </Button>
                  <Button variant="info" onClick={handleExportData}>
                    <i className="fas fa-download me-2"></i>
                    Export Data
                  </Button>
                  <Button variant="warning" onClick={handleSettings}>
                    <i className="fas fa-users-cog me-2"></i>
                    User Management
                  </Button>
                  <Button variant="secondary" onClick={() => setShowEventAdminModal(true)}>
                    <i className="fas fa-user-shield me-2"></i>
                    Event Admins
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Events Table */}
        <Row>
          <Col>
            <Card>
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0 fw-semibold">All Events</h5>
                <Badge bg="secondary">{dashboardData?.events?.length || 0} events</Badge>
              </Card.Header>
              <Card.Body className="p-0">
                {dashboardData?.events?.length === 0 ? (
                  <div className="text-center py-5">
                    <i className="fas fa-calendar-times display-1 text-muted mb-3"></i>
                    <h4 className="text-muted">No Events Found</h4>
                    <p className="text-muted">Start by creating your first event.</p>
                    <Button variant="primary" onClick={handleCreateEvent}>
                      <i className="fas fa-plus me-2"></i>
                      Create Event
                    </Button>
                  </div>
                ) : (
                  <div className="table-responsive">
                    <Table hover className="mb-0">
                      <thead className="bg-light">
                        <tr>
                          <th>Event Name</th>
                          <th>Category</th>
                          <th>Date</th>
                          <th>Registrations</th>
                          <th>Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData?.events?.map((event) => (
                          <tr key={event.id}>
                            <td>
                              <div className="fw-semibold">{event.title}</div>
                            </td>
                            <td>
                              <Badge bg={getCategoryColor(event.category)}>
                                {event.category}
                              </Badge>
                            </td>
                            <td>
                              <small className="text-muted">
                                {formatDate(event.eventDate)}
                              </small>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <span className="me-2">
                                  {event.registrationCount} / {event.maxParticipants}
                                </span>
                                <div className="progress flex-grow-1" style={{ height: '4px' }}>
                                  <div
                                    className="progress-bar bg-success"
                                    style={{
                                      width: `${(event.registrationCount / event.maxParticipants) * 100}%`
                                    }}
                                  ></div>
                                </div>
                              </div>
                            </td>
                            <td>
                              <Badge bg={event.isActive ? 'success' : 'secondary'}>
                                {event.isActive ? 'Active' : 'Inactive'}
                              </Badge>
                            </td>
                            <td>
                              <div className="d-flex gap-1 flex-wrap">
                                <Button
                                  variant="outline-primary"
                                  size="sm"
                                  onClick={() => handleEditEvent(event)}
                                  title="Edit Event"
                                >
                                  <i className="fas fa-edit"></i>
                                </Button>
                                <Button
                                  variant="outline-info"
                                  size="sm"
                                  onClick={() => handleViewEvent(event)}
                                  title="View Event"
                                >
                                  <i className="fas fa-eye"></i>
                                </Button>
                                <Button
                                  variant="outline-success"
                                  size="sm"
                                  onClick={() => handleViewParticipants(event)}
                                  title="View Participants"
                                >
                                  <i className="fas fa-users"></i>
                                </Button>
                                <Button
                                  variant="outline-danger"
                                  size="sm"
                                  onClick={() => handleDeleteEvent(event.id)}
                                  title="Delete Event"
                                >
                                  <i className="fas fa-trash"></i>
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Event Form Modal */}
      <EventFormModal
        show={showEventModal}
        onHide={() => {
          setShowEventModal(false);
          setSelectedEvent(null);
        }}
        event={isEditing ? selectedEvent : null}
        onSuccess={handleModalSuccess}
      />

      {/* Event View Modal */}
      <EventViewModal
        show={showViewModal}
        onHide={() => {
          setShowViewModal(false);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
        onEdit={handleEditEvent}
        onViewParticipants={handleViewParticipants}
      />

      {/* Participants Modal */}
      <ParticipantsModal
        show={showParticipantsModal}
        onHide={() => {
          setShowParticipantsModal(false);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
      />

      {/* User Management Modal */}
      <UserManagementModal
        show={showUserManagementModal}
        onHide={() => setShowUserManagementModal(false)}
      />

      {/* Event Admin Management Modal */}
      <Modal show={showEventAdminModal} onHide={() => setShowEventAdminModal(false)} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-user-shield me-2"></i>
            Event Admin Management
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="p-0">
          <EventAdminManagement />
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default Admin;
